# 新宿零点 · 一个小而真诚的文字冒险
# 保存为 xinshu_zero.py，并用 `python xinshu_zero.py` 运行

from dataclasses import dataclass, field

@dataclass
class GameState:
    location: str = "序章"
    inventory: set = field(default_factory=set)  # e.g., {"代币", "护身符"}
    flags: dict = field(default_factory=dict)    # e.g., {"answered_riddle": True}
    confession: str | None = None                # 玩家在结尾说出的那句真话

def say(text: str):
    print(text)

def prompt() -> str:
    return input("\n> ").strip().lower()

def show_help():
    say("可用指令：")
    say(" - 查看（看看四周/线索）")
    say(" - 左 / 右 / 上 / 下 / 返回（移动）")
    say(" - 对话 店员（在游戏厅）")
    say(" - 拿 物品名（拾取物品）")
    say(" - 投币（在神社）")
    say(" - 祈愿（在神社）")
    say(" - 背包（查看物品）")
    say(" - 帮助（查看此说明）")
    say(" - 退出（离开游戏）")

def show_inventory(state: GameState):
    if not state.inventory:
        say("你的背包是空的。")
    else:
        say("你随身携带： " + "、".join(sorted(state.inventory)))

def wait_for(state: GameState, handlers: dict[str, callable], fallback: callable | None = None):
    """
    handlers: 映射 指令 -> 处理函数
    fallback: 未匹配时的处理函数
    """
    while True:
        cmd = prompt()
        if cmd in ("帮助", "help", "？", "?"):
            show_help()
            continue
        if cmd in ("背包", "物品", "bag", "i"):
            show_inventory(state)
            continue
        if cmd in ("退出", "quit", "q", "bye"):
            say("你选择离开了这座城市。或许，离开本身也是一种答案。")
            raise SystemExit(0)
        # 简单移动别名
        if cmd in ("左", "向左"): cmd = "左"
        if cmd in ("右", "向右"): cmd = "右"
        if cmd in ("上", "向上"): cmd = "上"
        if cmd in ("下", "向下"): cmd = "下"
        if cmd in handlers:
            return handlers[cmd]()
        if fallback:
            fallback(cmd)
        else:
            say("你有些犹豫，不如试试“查看”或“帮助”。")

def scene_intro(state: GameState):
    say("【序章 · 新宿零点】")
    say("雨刚停。霓虹在水洼里碎成了无数个你。你站在一条狭窄的小巷口。")
    say("左边是还没打烊的游戏厅；右边是一座安静的小神社。")
    state.location = "小巷"

def scene_alley(state: GameState):
    say("\n【小巷】")
    say("风带着一丝暖意。你听见硬币滚动的声音，不确定来自哪里。")
    say("可去：左（游戏厅），右（神社）。你也可以“查看”。")

    def fallback(cmd: str):
        if cmd.startswith("查看"):
            say("巷口积水映着招牌：『零点之后，请对自己诚实。』楼梯通往屋顶的门紧锁着。")
            if state.flags.get("ready_for_rooftop"):
                say("你注意到楼梯门的红灯转为柔和的黄光，像在等待你的回答。你可以试着“上”。")
        else:
            say("你在原地犹豫了一下。也许该“左”或“右”。")

    def go_left():
        state.location = "游戏厅"
    def go_right():
        state.location = "神社"
    def go_up():
        if state.flags.get("ready_for_rooftop"):
            state.location = "楼梯间"
        else:
            say("门纹丝不动。也许你还缺点什么。")

    wait_for(state, {
        "左": go_left,
        "右": go_right,
        "上": go_up,
        "查看": lambda: fallback("查看"),
    }, fallback)

def scene_arcade(state: GameState):
    say("\n【游戏厅】")
    say("电子音在指尖跳动。角落有一台老式娃娃机，柜台后坐着困倦的店员。地上压着一张旧日记页。")
    say("你可以：“查看”、“对话 店员”、“拿 日记”、“返回”。")

    def talk_clerk():
        if state.flags.get("answered_riddle"):
            say("店员打了个呵欠：‘已经给过你代币了，祝你好运。’")
            return
        say("店员：‘答对一个谜语，我就给你一枚代币。’")
        say("他压低了声音：‘什么东西，分享出去后反而会变得更多？’")
        say("（提示：可以直接回答，比如：回答 快乐）")

        def riddle_fallback(cmd: str):
            if cmd.startswith("回答"):
                ans = cmd.replace("回答", "").strip()
                if ans in ("快乐", "喜悦", "幸福", "爱", "愛", "知识", "知識"):
                    say("店员笑了：‘对。给你。愿它带你去往想去的地方。’你得到：代币。")
                    state.inventory.add("代币")
                    state.flags["answered_riddle"] = True
                else:
                    say("店员摇头：‘不太对，但我喜欢你的答案。再想想？’")
            else:
                say("店员看着你，等你的答案。（用“回答 XXX”）")

        wait_for(state, {}, riddle_fallback)

    def take_note():
        if state.flags.get("read_note"):
            say("你已经把那页日记揣进了口袋。")
            return
        say("你捡起那页被水渍晕开的日记：")
        say("『如果今晚必须诚实一次，我想承认——我并不是无坚不摧。』")
        say("你轻轻折好，把它放进了口袋。")
        state.flags["read_note"] = True

    def fallback(cmd: str):
        if cmd.startswith("查看"):
            say("娃娃机里有一只小狐狸玩偶，像极了你小时候想要却没得到的那只。")
            say("柜台上放着一枚闪闪发亮的代币图样。")
            say("地上那页日记边缘卷起，像在等你。")
        elif cmd in ("返回", "回去", "back"):
            state.location = "小巷"
        else:
            say("音乐盖过了你的声音。也许先“对话 店员”或“拿 日记”。")

    wait_for(state, {
        "对话 店员": talk_clerk,
        "拿 日记": take_note,
        "返回": lambda: None,
        "查看": lambda: fallback("查看"),
    }, fallback)

def scene_shrine(state: GameState):
    say("\n【神社】")
    say("石灯笼里还留着一点点光。风铃清脆，像有人在说‘欢迎回来’。")
    say("你可以：“查看”、“投币”、“祈愿”、“返回”。")

    def offer_coin():
        if "代币" not in state.inventory:
            say("你摸了摸口袋，空空如也。也许在别处能换到一枚代币。")
            return
        if state.flags.get("offered"):
            say("你已经投过一次。神龛静静地回应着你的心跳。")
            return
        say("你把代币轻轻投入赛钱箱。叮——一声清亮。")
        state.inventory.remove("代币")
        state.flags["offered"] = True
        say("风铃一阵作响，一枚小小的护身符落在你的掌心。你得到：护身符。")
        state.inventory.add("护身符")
        maybe_ready_for_rooftop(state)

    def pray():
        say("你双手合十，闭上眼。请对自己许一个不夸张、但真实的愿望。")
        wish = input("愿望：").strip()
        if wish:
            say("风轻轻应了一声。你知道，至少今晚，你会为它走一步。")
            state.flags["wished"] = True
            maybe_ready_for_rooftop(state)
        else:
            say("你沉默了片刻。沉默也是一种回答。")

    def fallback(cmd: str):
        if cmd.startswith("查看"):
            say("赛钱箱很旧，但被擦得很干净。木牌上写着：『愿你有勇气，先对自己诚实。』")
        elif cmd in ("返回", "回去", "back"):
            state.location = "小巷"
        else:
            say("夜色很温柔，但不回答问题。试试“投币”或“祈愿”。")

    wait_for(state, {
        "投币": offer_coin,
        "祈愿": pray,
        "返回": lambda: None,
        "查看": lambda: fallback("查看"),
    }, fallback)

def maybe_ready_for_rooftop(state: GameState):
    # 满足两个条件中的关键一个：拿到护身符 + 读过日记 -> 解锁楼梯间提示
    if "护身符" in state.inventory and state.flags.get("read_note"):
        state.flags["ready_for_rooftop"] = True

def scene_stairwell(state: GameState):
    say("\n【楼梯间】")
    say("红色的应急灯像一颗稳定的心跳。铁门上有一行字：")
    say("『请对自己说一句真话，然后门会打开。』")
    say("（直接输入那句话。不需要指令词。）")
    conf = input("> ").strip()
    if not conf:
        say("门沉默不语。也许你可以再诚实一点。")
        return
    state.confession = conf
    say("你说完的那一刻，铁门解锁的声音轻轻响起。你走向屋顶。")
    state.location = "屋顶"

def scene_rooftop(state: GameState):
    say("\n【屋顶】")
    say("城市像一片温柔的海。风很轻，你把那句真话在心里又读了一遍：")
    say(f"『{state.confession}』")
    say("然后你笑了。不是因为一切都被解决，而是因为你终于开始了。")
    say("\n—— 完 ——")
    raise SystemExit(0)

SCENES = {
    "序章": scene_intro,
    "小巷": scene_alley,
    "游戏厅": scene_arcade,
    "神社": scene_shrine,
    "楼梯间": scene_stairwell,
    "屋顶": scene_rooftop,
}

def main():
    state = GameState()
    say("（提示：随时输入“帮助”查看指令，输入“退出”离开游戏。）")
    while True:
        SCENES[state.location](state)

if __name__ == "__main__":
    main()
